/* 主体内容区域 - 设置最大宽度和居中 */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
}

/* 私信页面标题区域 */
.message-header {
    margin: 20px 0;
}

.message-header h1 {
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    margin: 0;
}

/* 私信主容器 - 采用网格布局 */
.message-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    background-color: #1b1b1b;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-height: 725px; /* 调整高度以适应16:10比例 */
}

/* 联系人列表区域样式 */
.contact-list {
    background-color: #252525;
    border-right: 1px solid #333333;
    height: 725px; /* 增加高度，与容器一致 */
    display: flex;
    flex-direction: column;
}

/* 联系人搜索区域 */
.contact-search {
    padding: 15px;
    border-bottom: 1px solid #333333;
    position: relative;
}

.contact-search input {
    width: 100%;
    padding: 8px 35px 8px 15px;
    background-color: #333333;
    border: 1px solid #404040;
    color: #ffffff;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.contact-search input::placeholder {
    color: #999999;
}

.search-icon {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: #999999;
    font-size: 16px;
}

/* 联系人列表容器 */
.contacts {
    overflow-y: auto;
    flex: 1;
    
    /* 隐藏滚动条 - Firefox */
    scrollbar-width: none;
    
    /* 隐藏滚动条 - IE和旧版Edge */
    -ms-overflow-style: none;
}

/* 隐藏滚动条 - Webkit浏览器 */
.contacts::-webkit-scrollbar {
    width: 0;
    background: transparent;
    display: none;
}

/* 单个联系人样式 */
.contact {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #333333;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.contact:hover {
    background-color: #2a2a2a;
}

.contact.active {
    background-color: #303030;
}

/* 联系人头像 */
.contact-avatar {
    position: relative;
    margin-right: 15px;
    flex-shrink: 0;
}

.contact-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

/* 状态标识 */
.status-badge {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #252525;
}

.status-badge.online {
    background-color: #44b700;
}

.status-badge.offline {
    background-color: #999999;
}

.status-badge.away {
    background-color: #ff9900;
}

/* 联系人信息 */
.contact-info {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.contact-name {
    color: #ffffff;
    font-weight: bold;
    font-size: 15px;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-last-message {
    color: #999999;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 联系人元数据 */
.contact-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    min-width: 50px;
}

.contact-time {
    color: #999999;
    font-size: 12px;
    margin-bottom: 5px;
}

.unread-badge {
    background-color: #ff9900;
    color: #000000;
    font-size: 12px;
    font-weight: bold;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 聊天区域样式 */
.chat-area {
    display: flex;
    flex-direction: column;
    height: 725px; /* 增加高度，与容器一致 */
}

/* 聊天头部 */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #333333;
}

/* 聊天用户信息 */
.chat-user-info {
    display: flex;
    align-items: center;
}

.chat-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
}

.chat-user-name {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 2px;
}

.chat-user-status {
    font-size: 12px;
    color: #44b700;
}

/* 聊天操作按钮 */
.chat-actions {
    display: flex;
    gap: 15px;
}

.chat-action-btn {
    background: none;
    border: none;
    color: #999999;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 5px;
    border-radius: 50%;
}

.chat-action-btn:hover {
    color: #ff9900;
    background-color: #2a2a2a;
}

.action-icon {
    font-size: 18px;
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    /* 隐藏滚动条 - Firefox */
    scrollbar-width: thin;
    scrollbar-color: #333333 #1b1b1b;
}

/* 自定义滚动条样式 - Webkit浏览器 */
.chat-messages::-webkit-scrollbar {
    width: 5px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #1b1b1b;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #333333;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #444444;
}

/* 日期分隔线 */
.date-separator {
    text-align: center;
    margin: 10px 0;
    position: relative;
}

.date-separator span {
    background-color: #1b1b1b;
    padding: 0 10px;
    color: #999999;
    font-size: 12px;
    position: relative;
    z-index: 1;
}

.date-separator::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
    background-color: #333333;
    z-index: 0;
}

/* 消息通用样式 */
.message {
    display: flex;
    margin-bottom: 10px;
    max-width: 80%;
}

/* 接收到的消息样式 */
.message.received {
    align-self: flex-start;
}

/* 发送的消息样式 */
.message.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0;
}

.message.sent .message-avatar {
    display: none;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-content {
    display: flex;
    flex-direction: column;
}

.message-bubble {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 100%;
    word-break: break-word;
}

.message.received .message-bubble {
    background-color: #303030;
    border-top-left-radius: 5px;
}

.message.sent .message-bubble {
    background-color: #ff9900;
    color: #000000;
    border-top-right-radius: 5px;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    color: #999999;
    margin-top: 2px;
    align-self: flex-end;
}

.message.sent .message-time {
    align-self: flex-start;
}

/* 打字指示器样式 */
.typing-indicator .message-bubble {
    min-width: 60px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.typing-indicator .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #999999;
    margin: 0 2px;
    animation: typing-animation 1.4s infinite ease-in-out both;
}

.typing-indicator .dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-animation {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.6;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 错误消息样式 */
.message.error .message-bubble {
    background-color: #4a2128;
    border: 1px solid #6e2b35;
}

.message.error .message-text {
    color: #ff6b81;
}

.message.error .message-text small {
    color: #cc5566;
    font-size: 12px;
}

/* 聊天输入区域 */
.chat-input-area {
    padding: 15px 20px;
    border-top: 1px solid #333333;
}

/* 工具栏 */
.chat-toolbar {
    display: flex;
    margin-bottom: 10px;
    gap: 10px;
}

.toolbar-btn {
    background: none;
    border: none;
    color: #999999;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 5px 10px;
    border-radius: 5px;
}

.toolbar-btn:hover {
    color: #ff9900;
    background-color: #2a2a2a;
}

.toolbar-icon {
    font-size: 18px;
}

/* 输入框容器 */
.chat-input-container {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

/* 消息输入框 */
#messageInput {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 10px 15px;
    border: 1px solid #333333;
    border-radius: 20px;
    background-color: #252525;
    color: #ffffff;
    font-size: 14px;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;
    font-family: inherit;
}

#messageInput:focus {
    border-color: #ff9900;
}

#messageInput::placeholder {
    color: #999999;
}

/* 发送按钮 */
.send-button {
    background-color: #ff9900;
    color: #000000;
    border: none;
    height: 40px;
    padding: 0 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.send-button:hover {
    background-color: #e69500;
}

.send-button:disabled {
    background-color: #4f4f4f;
    color: #999999;
    cursor: not-allowed;
}

/* 空状态提示 */
.chat-empty-state {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #999999;
    padding: 20px;
    text-align: center;
}

.empty-state-icon {
    font-size: 64px;
    margin-bottom: 20px;
    color: #ff9900;
    opacity: 0.6;
}

.chat-empty-state h3 {
    font-size: 20px;
    color: #ffffff;
    margin-bottom: 10px;
}

.chat-empty-state p {
    font-size: 14px;
    margin-bottom: 20px;
}

.new-chat-btn {
    background-color: #ff9900;
    color: #000000;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.new-chat-btn:hover {
    background-color: #e69500;
}

/* 响应式布局 */
@media (max-width: 1024px) {
    .message-container {
        grid-template-columns: 250px 1fr;
    }
}

@media (max-width: 768px) {
    .message-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .contact-list {
        height: 500px;
        border-right: none;
        border-bottom: 1px solid #333333;
    }
    
    .chat-area {
        height: 600px;
    }
}

@media (max-width: 480px) {
    .contact-list {
        height: 400px;
    }
    
    .chat-area {
        height: 500px;
    }
    
    .message {
        max-width: 90%;
    }
    
    .message-avatar {
        width: 30px;
        height: 30px;
    }
    
    .contact-avatar img {
        width: 40px;
        height: 40px;
    }
} 