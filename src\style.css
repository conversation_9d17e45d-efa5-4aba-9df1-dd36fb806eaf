/* 设置页面默认缩放比例为90% */
html {
    zoom: 0.9; /* 适用于大多数浏览器 */
    -moz-transform: scale(0.9); /* Firefox */
    -moz-transform-origin: 0 0;
}

/* 轮播图容器样式 */
.carousel-container {
    grid-column: 1 / -1;
    height: 400px; /* 固定高度，约为视频卡片高度的2倍 */
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background-color: #1b1b1b;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.carousel {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.carousel-slide {
    width: 100%;
    height: 100%;
    position: absolute;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
}

.carousel-title {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.carousel-description {
    font-size: 1rem;
    opacity: 0.9;
}

.carousel-nav {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.carousel-dot.active {
    background-color: #ff9900;
}

.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    transition: background-color 0.3s ease;
    z-index: 10;
}

.carousel-arrow:hover {
    background-color: rgba(255, 153, 0, 0.8);
}

.carousel-arrow-prev {
    left: 20px;
}

.carousel-arrow-next {
    right: 20px;
}

/* 页面body元素的全局基础样式 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #000000;
    color: #ffffff;
    line-height: 1.6;
    font-size: 16px;
    padding-top: 60px; /* 添加与header高度相同的内边距，避免内容被遮挡 */
}

/* 主体内容区域 - 设置最大宽度和居中 */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
}

/* 页面头部的基础样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1b1b1b;
    padding: 10px 10px;
    border-bottom: 1px solid #292929;
    gap: 10px;
    position: fixed; /* 改为固定定位 */
    top: 0; /* 固定在顶部 */
    left: 0; /* 确保左边缘对齐 */
    right: 0; /* 确保右边缘对齐 */
    z-index: 1000; /* 确保显示在其他元素上方 */
    transition: all 0.3s ease; /* 添加平滑过渡效果 */
    width: 100%; /* 设置宽度占满整个视口 */
    box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

/* Logo容器的样式，用于包裹Logo相关的元素 */
.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Logo文字部分的样式 */
.logo-container.logo-text {
    font-family: "Arial", sans-serif;
    font-size: 1.8rem;
    font-weight: bold;
    color: #ffffff;
    text-decoration: none;
}

/* Logo文字中特定span元素（如"Hub"部分）的样式 */
.logo-container.logo-text span {
    color: #ffa31a;
    font-weight: normal;
}

/* Logo图片容器的样式（如果使用图片Logo） */
.logo-container.logo-image {
    height: 40px;
    width: auto;
    vertical-align: middle;
}

/* "ph-logo"（仿Pornhub风格Logo）的整体容器样式 */
.ph-logo {
    display: inline-flex;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 32px;
    font-weight: bold;
    line-height: 1.2;
    text-align: center;
    border-radius: 8px;
    overflow: hidden;
}

/* "ph-logo"中"Porn"部分的样式 */
.ph-logo-porn {
    background: rgba(0, 0, 0, 0);
    color: #FFFFFF;
    padding: 5px 10px;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

/* "ph-logo"中"Hub"部分的样式 */
.ph-logo-hub {
    background-color: #FF9900;
    color: #000000;
    padding: 5px 10px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    margin-left: -4px;
}

/* 导航栏无序列表的基础样式 */
nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
}

/* 导航栏列表项的样式 */
nav ul li {
    margin-left: 10px;
}

/* 导航栏链接的样式 */
nav ul li a {
    text-decoration: none;
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 导航栏链接悬浮或激活状态的样式 */
nav ul li a:hover,
nav ul li a.active {
    color: #1b1b1b;
    background-color: #ff9900;
}

/* 修改左侧导航栏样式 */
.nav-ul-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-grow: 1;
    margin-left: 0px;
}

/* 修改右侧导航栏样式 */
.nav-ul-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-grow: 1;
    margin-right: 20px;
}

/* 汉堡菜单图标的容器样式 */
.hamburger-menu {
    width: 24px;
    height: 18px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
}

/* 汉堡菜单图标中每一横的样式 */
.hamburger-menu span {
    display: block;
    height: 3px;
    width: 100%;
    background-color: #ff9900;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 汉堡菜单图标悬浮时的横条样式 */
.hamburger-menu:hover span {
    background-color: #ffa31a;
}

/* 汉堡菜单激活状态 - 第一条线旋转 */
.hamburger-menu.active span:first-child {
    transform: translateY(7.5px) rotate(45deg);
}

/* 汉堡菜单激活状态 - 中间线消失 */
.hamburger-menu.active span:nth-child(2) {
    opacity: 0;
}

/* 汉堡菜单激活状态 - 第三条线旋转 */
.hamburger-menu.active span:last-child {
    transform: translateY(-7.5px) rotate(-45deg);
}

/* 桌面导航按钮（包裹汉堡菜单）的样式 */
#desktopNavigation {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
}

/* 桌面导航按钮悬浮效果 */
#desktopNavigation:hover {
    opacity: 0.8;
}

/* 搜索栏容器的样式 */
.search-bar {
    display: flex;
    align-items: center;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
}

/* 搜索栏输入框的样式 */
.search-bar input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #292929;
    background-color: #292929;
    color: #ffffff;
    border-radius: 4px 0 0 4px;
    outline: none;
    font-size: 0.9rem;
    min-width: 250px;
}

/* 搜索栏输入框占位符文本的样式 */
.search-bar input[type="text"]::placeholder {
    color: #808080;
    width: 300px;
}

/* 搜索栏提交按钮的样式 */
.search-bar button[type="submit"] {
    padding: 8px 15px;
    background-color: #ffa31a;
    color: #1b1b1b;
    border: 1px solid #ffa31a;
    border-left: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

/* 搜索栏提交按钮悬浮效果 */
.search-bar button[type="submit"]:hover {
    background-color: #e69500;
}

/* 搜索历史栏容器 */
.search-dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 24px);
    max-width: 400px;
    z-index: 1000;
    display: none;
    margin-top: 2px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* 搜索历史栏内容 */
.search-dropdown-content {
    background-color: #1b1b1b;
    border: 1px solid #484848;
    border-radius: 4px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    overflow: hidden;
}

/* 搜索历史栏样式 */
.search-dropdown-content a {
    color: #ffffff;
    padding: 10px 16px;
    text-decoration: none;
    display: block;
    font-size: 14px;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #292929;
}

/* 搜索历史栏链接悬浮效果 */
.search-dropdown-content a:hover {
    background-color: #292929;
    color: #ff9900;
}

/* 搜索栏悬浮效果 */
.search-bar:focus-within + .search-dropdown {
    display: block;
    opacity: 1;
    visibility: visible;
    animation: fadeIn 0.3s ease;
}

/* 头像容器样式 */
.avatar-info {
    position: relative;
    margin-left: 10px;
    margin-right: 10px;
}

/* 头像触发器样式 */
.avatar-trigger {
    display: block;
    cursor: pointer;
    text-decoration: none;
}

/* 头像图片样式 */
.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    object-fit: cover;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.avatar:hover {
    border-color: #ff9900;
}

/* 头像二级菜单 */
.avatar-dropdown {
    display: none;
    position: absolute;
    top: 70px;
    right: 0;
    background-color: #1b1b1b;
    border: 1px solid #292929;
    border-radius: 4px;
    min-width: 260px;
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* 显示头像二级菜单 */
.avatar-dropdown.show {
    display: block;
}

/* 头像二级菜单内容 */
.avatar-dropdown-content {
    padding: 15px;
}

/* 用户信息区域 */
.user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom: 1px solid #292929;
    padding-bottom: 15px;
    margin-bottom: 10px;
}

/* 大头像 */
.avatar-large {
    width: 64px;
    height: 64px;
    margin-bottom: 10px;
}

.avatar-large img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ff9900;
}

/* 用户名样式 */
.username {
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

/* 用户等级 */
.user-level {
    display: flex;
    gap: 5px;
    margin-top: 5px;
}

.level-tag {
    background-color: #00a0d6;
    color: white;
    padding: 1px 5px;
    border-radius: 3px;
    font-size: 12px;
}

.vip-tag {
    background-color: #ff9900;
    color: #000000;
    padding: 1px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

/* 菜单组 */
.menu-group {
    margin: 10px 0;
}

.menu-group.border-top {
    border-top: 1px solid #292929;
    padding-top: 10px;
}

/* 菜单项 */
.menu-group a {
    display: flex;
    align-items: center;
    padding: 8px 0;
    color: #cccccc;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;
}

.menu-group a:hover {
    color: #ff9900;
}

.menu-icon {
    margin-right: 8px;
    font-style: normal;
}

/* 登录区域 */
.login-section {
    padding: 10px 0;
}

.login-section p {
    color: #cccccc;
    margin-bottom: 10px;
    font-size: 14px;
}

.login-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.login-benefits li {
    color: #999999;
    font-size: 13px;
    margin-bottom: 5px;
}

.login-btn {
    display: block;
    background-color: #ff9900;
    color: #000000;
    text-align: center;
    padding: 8px 0;
    border-radius: 4px;
    font-weight: bold;
    font-size: 14px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.login-btn:hover {
    background-color: #e69500;
}

/* 用户下拉菜单容器 */
.user-dropdown {
    display: none;
    position: absolute;
    top: 40px;
    right: 0;
    background-color: #1b1b1b;
    border: 1px solid #292929;
    border-radius: 4px;
    padding: 10px;
    min-width: 120px;
    z-index: 100;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 用户下拉菜单链接样式 */
.user-dropdown a {
    display: block;
    color: #ff9900;
    text-decoration: none;
    padding: 5px 0;
    font-size: 14px;
    transition: color 0.3s ease;
}

.user-dropdown a:hover {
    color: #ffa31a;
    text-decoration: underline;
}

/* 显示用户下拉菜单 */
.avatar-dropdown-content:hover .user-dropdown {
    display: block;
}

/* 登录提示样式 */
.login-prompt {
    display: none;
    position: absolute;
    top: 40px;
    right: 0;
    background-color: #1b1b1b;
    border: 1px solid #292929;
    border-radius: 4px;
    padding: 10px;
    color: #ff9900;
    font-size: 14px;
    z-index: 100;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.avatar-dropdown-content:hover .login-prompt {
    display: block;
}

/* 下拉菜单容器 */
.dropdown {
    position: relative;
    display: inline-block;
}

/* 下拉菜单内容 */
.dropdown-content {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #1b1b1b;
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    border-radius: 6px;
    padding: 8px 0;
    z-index: 1000;
    border: 1px solid #484848;
    margin-top: 28px;
}

/* 下拉菜单链接样式 */
.dropdown-content a {
    color: #ffffff;
    padding: 8px 16px;
    text-decoration: none;
    display: block;
    font-size: 14px;
    transition: all 0.3s ease;
}

/* 下拉菜单链接悬浮效果 */
.dropdown-content a:hover {
    background-color: #292929;
    color: #ff9900;
}

/* 显示下拉菜单 */
.dropdown:hover .dropdown-content {
    opacity: 1;
    visibility: visible;
    /* display: block; */
    animation: fadeIn 0.3s ease;
}

/* 调整三角形图标位置 */
.dropdown > a::after {
    content: '';
    display: inline-block;
    margin-left: 5px;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    border-color: #ffffff transparent transparent transparent;
    vertical-align: middle;
    transition: transform 0.3s ease;
}

/* 悬浮时旋转三角形图标 */
.dropdown:hover > a::after {
    transform: rotate(180deg);
}

/* 鼠标离开时的样式 */
.dropdown:not(:hover) .dropdown-content {
    animation: fadeOut 0.3s ease forwards;
}

/* 多列下拉菜单容器 */
.dropdown-content.multi-column {
    display: flex;
    flex-direction: row;
    min-width: 600px;
    padding: 10px;
    gap: 10px;
    border: 1px solid #484848;
}

/* 每一列的样式 */
.dropdown-content.multi-column .column {
    flex: 1;
    min-width: 140px;
    padding: 5px;
    border-right: 1px solid #292929;
}

/* 最后一列移除右边框 */
.dropdown-content.multi-column .column:last-child {
    border-right: none;
}

/* 调整链接样式 */
.dropdown-content.multi-column a {
    padding: 6px 12px;
    white-space: nowrap;
    font-size: 13px;
}

/* 链接悬浮效果 */
.dropdown-content.multi-column a:hover {
    background-color: #292929;
    color: #ff9900;
    border-radius: 4px;
}

/* 视频卡片的基础样式 */
.video-card {
    width: 97%;
    height: auto;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease-in-out;
}

/* 视频卡片悬浮效果 */
.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* 视频卡片缩略图链接容器的样式，用于保持16:9比例 */
.thumbnail-link {
    position: relative;
    display: block;
    width: 100%;
    padding-top: 56.25%;
    background: #000;
    overflow: hidden;
}

/* 视频卡片图片的样式，确保填充缩略图容器并保持比例 */
.video-card img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* 视频时长标签的样式 */
.duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.75);
    color: #ffffff;
    padding: 2px 6px;
    font-size: 0.75rem;
    border-radius: 2px;
    z-index: 1;
}

/* 视频信息区域的样式（标题、元数据等） */
.video-info {
    padding: 0px;
}

/* 视频标题的样式，包括多行省略效果 */
.video-title {
    font-size: 1rem;
    font-weight: bold;
    margin: 0 0 8px 0;
    line-height: 1.3;
    display: -webkit-box;
    /* -webkit-line-clamp: 2; */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2.6em;
}

/* 视频标题内链接的样式 */
.video-title a {
    color: #ffffff;
    text-decoration: none;
}

/* 视频标题内链接悬浮效果 */
.video-title a:hover {
    color: #ffa31a;
}

/* 视频元数据（观看次数、上传时间）的样式 */
.video-meta {
    font-size: 0.8rem;
    color: #808080;
}

/* 视频元数据中每个span元素的样式 */
.video-meta span {
    margin-right: 10px;
}

/* 视频元数据中最后一个span元素的特殊处理（移除右边距） */
.video-meta span:last-child {
    margin-right: 0;
}

/* 视频网格容器的样式 */
.video-grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0px;
    padding: 20px 40px;
    max-width: 1440px;
    margin: 0 auto;
}

/* 页面页脚的基础样式 */
footer {
    background: #1b1b1b;
    color: #808080;
    padding: 30px 20px;
    border-top: 1px solid #292929;
}

/* 页脚头部信息区域（例如声明文本）的样式 */
.footer-head {
    color: #ffffff;
    font-size: 0.8rem;
    margin-bottom: 20px;
    text-align: center;
    padding: 0 0;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* 页脚主要内容区域（包含多列链接）的样式 */
.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 60px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 页脚中每一列的样式 */
.footer-column {
    flex: 1;
    min-width: 180px;
    padding: 0 20px;
}

/* 页脚列中标题（h4）的样式 */
.footer-column h4 {
    color: #808080;
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: bold;
    padding-left: 0;
}

/* 页脚列中无序列表的样式 */
.footer-column ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

/* 页脚列中列表项的样式 */
.footer-column ul li {
    margin-bottom: 8px;
}

/* 页脚列中链接的样式 */
.footer-column ul li a {
    color: #ff9900;
    text-decoration: none;
    transition: color 0.2s ease;
}

/* 页脚列中链接悬浮效果 */
.footer-column ul li a:hover {
    text-decoration: underline;
}

/* 页脚底部区域（如版权信息）的样式 */
.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #292929;
    font-size: 0.85rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* 全局链接的基础样式 */
a {
    color: #ffa31a;
    text-decoration: none;
}

/* 全局链接悬浮效果 */
/* a:hover {
    text-decoration: underline;
} */

/* 全局盒子模型设置为border-box，应用于所有元素及其伪元素 */
*,
*::before,
*::after {
    box-sizing: border-box;
}

/* 添加动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 添加淡出动画效果 */
@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 设置响应式 */
@media (max-width: 1024px) {
    .carousel-container {
        height: 350px;
    }

    /* 视频网格容器在中等屏幕下的样式 */
    .video-grid-container {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        padding: 15px;
    }

    /* 视频卡片图片在中等屏幕下的样式，保持宽高比 */
    .video-card img {
        aspect-ratio: 16/9;
        object-fit: cover;
    }

    /* 头部在中等屏幕下的内边距调整 */
    header {
        padding: 10px 15px;
    }

    /* 导航列表项在中等屏幕下的左边距调整 */
    nav ul li {
        margin-left: 15px;
    }

    /* 搜索框输入栏在中等屏幕下的最小宽度调整 */
    .search-bar input[type="text"] {
        min-width: 200px;
    }
}

@media (max-width: 768px) {
    .carousel-container {
        height: 300px;
    }

    /* 头部在小型屏幕下的布局调整为垂直堆叠 */
    header {
        flex-direction: column;
        align-items: flex-start;
    }

    /* 导航栏在小型屏幕下占满宽度并增加上边距 */
    nav {
        width: 100%;
        margin-top: 10px;
    }

    /* 导航列表在小型屏幕下垂直堆叠 */
    nav ul {
        flex-direction: column;
        width: 100%;
    }

    /* 导航列表项在小型屏幕下的样式调整 */
    nav ul li {
        margin-left: 0;
        margin-bottom: 5px;
        width: 100%;
    }

    /* 导航链接在小型屏幕下块状显示并居中 */
    nav ul li a {
        display: block;
        text-align: center;
        padding: 10px;
    }

    /* 搜索栏在小型屏幕下占满宽度并增加上边距 */
    .search-bar {
        width: 100%;
        margin-top: 10px;
    }

    /* 搜索输入框在小型屏幕下占据剩余空间 */
    .search-bar input[type="text"] {
        flex-grow: 1;
    }

    /* 搜索下拉框在小型屏幕下的样式调整 */
    .search-dropdown {
        width: 100%;
        max-width: none;
        position: relative;
        margin-top: 5px;
        transform: none;
        left: 0;
        display: none;
    }
    
    /* 搜索栏及下拉框在小型屏幕下的容器 */
    .search-bar {
        position: relative;
        transform: none;
        left: auto;
        width: 100%;
        margin: 10px 0;
    }

    /* 视频网格容器在小型屏幕下的样式调整 */
    .video-grid-container {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 10px;
        padding: 10px;
    }

    /* 视频标题在小型屏幕下的字号调整 */
    .video-title {
        font-size: 0.9rem;
    }

    /* 页脚内容区域在小型屏幕下垂直堆叠 */
    .footer-content {
        flex-direction: column;
    }

    /* 页脚列在小型屏幕下的样式调整 */
    .footer-column {
        margin-bottom: 20px;
        min-width: 100%;
    }

    /* 页脚最后一列在小型屏幕下移除底部外边距 */
    .footer-column:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 480px) {
    .carousel-container {
        height: 250px;
    }

    /* body在超小型屏幕下的基础字号调整 */
    body {
        font-size: 14px;
    }

    /* Logo文字在超小型屏幕下的字号调整 */
    .logo-container.logo-text {
        font-size: 1.5rem;
    }

    /* 头部在超小型屏幕下的内边距调整 */
    header {
        padding: 10px;
    }

    /* 视频网格容器在超小型屏幕下的样式调整 */
    .video-grid-container {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        padding: 10px 5px;
    }

    /* 页脚列标题在超小型屏幕下的字号调整 */
    .footer-column h4 {
        font-size: 1rem;
    }

    /* 页脚列链接在超小型屏幕下的字号调整 */
    .footer-column ul li a {
        font-size: 0.85rem;
    }
}

/* 免责声明样式 */
.disclaimer {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: rgba(255, 153, 0, 0.1);
    border-left: 3px solid #ff9900;
    font-size: 0.8rem;
    line-height: 1.4;
    color: #cccccc;
    text-align: left;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    border-radius: 3px;
}

.disclaimer p {
    margin: 0;
}

.disclaimer strong {
    color: #ff9900;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    left: -280px; /* 初始位置在屏幕外 */
    top: 0; /* 从屏幕顶部开始 */
    width: 280px;
    height: 100vh; /* 占据整个视口高度 */
    min-height: 100%; /* 确保至少覆盖整个页面 */
    background-color: #1b1b1b;
    border-right: 1px solid #292929;
    z-index: 999;
    transition: left 0.3s ease;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
    bottom: 0; /* 确保扩展到底部 */
    
    /* 隐藏滚动条 - Firefox */
    scrollbar-width: none;
    
    /* 隐藏滚动条 - IE和旧版Edge */
    -ms-overflow-style: none;
}

/* 隐藏滚动条 - Webkit浏览器 (Chrome, Safari, Edge等) */
.sidebar::-webkit-scrollbar {
    width: 0;
    background: transparent;
    display: none;
}

/* 为侧边栏内容添加顶部内边距，避免被顶栏遮挡 */
.sidebar-menu:first-child {
    margin-top: 60px; /* 与顶栏高度一致 */
}

/* 侧边栏打开时 */
.sidebar.open {
    left: 0;
    height: 100vh; /* 确保视口高度 */
    min-height: 100%; /* 确保至少覆盖整个页面 */
    top: 0;
    bottom: 0; /* 确保侧边栏扩展到底部 */
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0; /* 从屏幕顶部开始 */
    left: 0;
    width: 100%;
    height: 100vh; /* 占据整个视口高度 */
    min-height: 100%; /* 确保至少覆盖整个页面 */
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    display: none;
    /* 确保遮罩层完全覆盖 */
    top: 0;
    right: 0;
    bottom: 0;
}

/* 遮罩层显示 */
.sidebar-overlay.show {
    display: block;
}

/* 侧边栏菜单项样式 */
.sidebar-menu {
    padding: 12px 0;
}

.sidebar-menu-item {
    padding: 10px 24px;
    display: flex;
    align-items: center;
    color: #ffffff;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.sidebar-menu-item:hover {
    background-color: #292929;
}

.sidebar-menu-item.active {
    background-color: #303030;
}

.sidebar-menu-icon {
    margin-right: 24px;
    font-size: 18px;
    width: 24px;
    text-align: center;
    color: #ff9900;
}

.sidebar-section-title {
    padding: 16px 24px 8px;
    color: #aaaaaa;
    font-size: 16px;
    font-weight: bold;
    border-top: 1px solid #292929;
    margin-top: 8px;
}
