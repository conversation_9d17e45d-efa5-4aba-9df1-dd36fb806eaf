<!DOCTYPE html>
<html lang="zh-hans">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=0.9">
    <title>Mainpage - Bili-Hub</title>
    <link href="./style.css" rel="stylesheet" type="text/css">
    <link href="./message.css" rel="stylesheet" type="text/css">
    <script src="./search.js" defer></script>
    <script type="module" src="./message.js"></script>
</head>

<body>
    <!-- 页面头部 - 包含导航栏、搜索框和用户信息 -->
    <header>
        <!-- LOGO区域 - 左侧汉堡菜单和LOGO标志 -->
        <div class="logo-container">
                <button id="desktopNavigation" type="button" aria-label="Desktop Navigation">
                    <div class="hamburger-menu" id="hamburger">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>
            <div class="ph-logo">
                <span class="ph-logo-porn">Bili</span><span class="ph-logo-hub">Hub</span>
            </div>
        </div>
        
        <!-- 左侧导航菜单 - 首页、分类、频道等 -->
        <nav class="nav-ul-left">
            <ul>
                <li class="dropdown">
                    <a href="./index.html">首页</a>
                    <div class="dropdown-content multi-column">
                        <!-- 首页下拉菜单 - 分为5列显示不同分类 -->
                        <div class="column">
                            <a href="https://www.bilibili.com/anime/" target="_blank">🎬 番剧</a>
                            <a href="https://www.bilibili.com/movie/" target="_blank">🎦 电影</a>
                            <a href="https://www.bilibili.com/guochuang/" target="_blank">🎨 国创</a>
                            <a href="https://www.bilibili.com/tv/" target="_blank">📺 电视剧</a>
                            <a href="https://www.bilibili.com/variety/" target="_blank">🎭 综艺</a>
                            <a href="https://www.bilibili.com/documentary/" target="_blank">📹 纪录片</a>
                            <a href="https://www.bilibili.com/c/douga/" target="_blank">🎮 动画</a>
                            <a href="https://www.bilibili.com/c/game/" target="_blank">🎲 游戏</a>
                            <a href="https://www.bilibili.com/c/kichiku/" target="_blank">🎵 鬼畜</a>
                            <a href="https://www.bilibili.com/c/music/" target="_blank">🎼 音乐</a>
                        </div>
                        <div class="column">
                            <a href="https://www.bilibili.com/c/dance/" target="_blank">💃 舞蹈</a>
                            <a href="https://www.bilibili.com/c/cinephile/" target="_blank">🎥 影视</a>
                            <a href="https://www.bilibili.com/c/ent/" target="_blank">🎪 娱乐</a>
                            <a href="https://www.bilibili.com/c/knowledge/" target="_blank">📚 知识</a>
                            <a href="https://www.bilibili.com/c/tech/" target="_blank">📱 科技数码</a>
                            <a href="https://www.bilibili.com/c/information/" target="_blank">📰 资讯</a>
                            <a href="https://www.bilibili.com/c/food/" target="_blank">🍜 美食</a>
                            <a href="https://www.bilibili.com/c/shortplay/" target="_blank">🎭 小剧场</a>
                            <a href="https://www.bilibili.com/c/car/" target="_blank">🚗 汽车</a>
                            <a href="https://www.bilibili.com/c/fashion/" target="_blank">💄 时尚美妆</a>
                        </div>
                        <div class="column">
                            <a href="https://www.bilibili.com/c/sports/" target="_blank">⚽ 体育运动</a>
                            <a href="https://www.bilibili.com/c/animal/" target="_blank">🐾 动物</a>
                            <a href="https://www.bilibili.com/c/vlog/" target="_blank">📽️ vlog</a>
                            <a href="https://www.bilibili.com/c/painting/" target="_blank">🎨 绘画</a>
                            <a href="https://www.bilibili.com/c/ai/" target="_blank">🤖 人工智能</a>
                            <a href="https://www.bilibili.com/c/home/" target="_blank">🏠 家装房产</a>
                            <a href="https://www.bilibili.com/c/outdoors/" target="_blank">👟 户外潮流</a>
                            <a href="https://www.bilibili.com/c/gym/" target="_blank">💪 健身</a>
                            <a href="https://www.bilibili.com/c/handmake/" target="_blank">🎨 手工</a>
                            <a href="https://www.bilibili.com/c/travel/" target="_blank">✈️ 旅游出行</a>
                        </div>
                        <div class="column">
                            <a href="https://www.bilibili.com/c/rural/" target="_blank">🌾 三农</a>
                            <a href="https://www.bilibili.com/c/parenting/" target="_blank">👶 亲子</a>
                            <a href="https://www.bilibili.com/c/health/" target="_blank">❤️ 健康</a>
                            <a href="https://www.bilibili.com/c/emotion/" target="_blank">💝 情感</a>
                            <a href="https://www.bilibili.com/c/life_joy/" target="_blank">🎯 生活兴趣</a>
                            <a href="https://www.bilibili.com/c/life_experience/" target="_blank">📝 生活经验</a>
                            <a href="https://love.bilibili.com/" target="_blank">🤝 公益</a>
                            <a href="https://www.bilibili.com/blackboard/era/Vp41b8bsU9Wkog3X.html" target="_blank">🎞️
                                超高清</a>
                        </div>
                        <div class="column">
                            <a href="https://www.bilibili.com/read/home/" target="_blank">📑 专栏</a>
                            <a href="https://live.bilibili.com/" target="_blank">🎥 直播</a>
                            <a href="https://www.bilibili.com/blackboard/activity-list.html?" target="_blank">🎪 活动</a>
                            <a href="https://www.bilibili.com/cheese/" target="_blank">📚 课堂</a>
                            <a href="https://www.bilibili.com/blackboard/activity-5zJxM3spoS.html" target="_blank">👥
                                社区中心</a>
                            <a href="https://music.bilibili.com/pc/music-center/" target="_blank">🎵 新歌热榜</a>
                        </div>
                    </div>
                </li>
                <li class="dropdown"><a href="">番剧</a></li>
                <li class="dropdown"><a href="">直播</a></li>
                <li class="dropdown"><a href="">游戏中心</a></li>
                <li class="dropdown"><a href="">会员购</a></li>
                <li class="dropdown"><a href="">漫画</a></li>
                <li class="dropdown"><a href="">赛事</a></li>
            </ul>
        </nav>
        
        <!-- 搜索框区域 - 中间的搜索功能 -->
        <form class="search-bar" action="#" method="get" onsubmit="return handleSearch(event)">
            <input type="text" name="keyword" id="searchInput" placeholder="搜索视频..." required>
            <button type="submit">搜索</button>
        </form>
        <div class="search-dropdown">
            <!-- 搜索历史下拉框 - 通过JavaScript动态生成 -->
        </div>
        
        <!-- 右侧导航菜单 - 大会员、消息、动态等功能 -->
        <nav class="nav-ul-right">
            <ul>
                <li class="dropdown"><a href="">大会员</a></li>
                <li class="dropdown">
                    <a href="./message.html" target="_blank">消息</a>
                    <div class="dropdown-content">
                        <a href="./message.html" target="_blank">回复我的</a>
                        <a href="./message.html" target="_blank">@我的</a>
                        <a href="./message.html" target="_blank">收到的赞</a>
                        <a href="./message.html" target="_blank">系统消息</a>
                        <a href="./message.html" target="_blank">我的消息</a>
                    </div>
                </li>
                <li class="dropdown"><a href="https://t.bilibili.com/" target="_blank">动态</a></li>
                <li class="dropdown"><a href="">收藏</a></li>
                <li class="dropdown"><a href="https://www.bilibili.com/history" target="_blank">历史</a></li>
                <li class="dropdown"><a href="https://member.bilibili.com/platform/home" target="_blank">创作中心</a></li>
                <li class="dropdown">
                    <a href="https://member.bilibili.com/platform/upload/video/frame" target="_blank">投稿</a>
                    <div class="dropdown-content">
                        <a href="https://member.bilibili.com/platform/upload/text/apply" target="_blank">专栏投稿</a>
                        <a href="https://member.bilibili.com/platform/upload/audio/frame" target="_blank">音频投稿</a>
                        <a href="https://member.bilibili.com/platform/upload/sticker" target="_blank">贴纸投稿</a>
                        <a href="https://member.bilibili.com/platform/upload/video/frame" target="_blank">视频投稿</a>
                        <a href="https://member.bilibili.com/platform/upload-manager/article" target="_blank">投稿管理</a>
                    </div>
                </li>
            </ul>
        </nav>
        
        <!-- 用户头像区域 - 右侧用户信息和下拉菜单 -->
        <div class="avatar-info dropdown">
            <!-- 用户头像区域由script.js动态控制 -->
            <div id="avatar-dropdown" class="avatar-dropdown">
                <!-- 动态内容将由script.js填充 -->
            </div>
        </div>
    </header>

    <!-- 左侧边栏 -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-menu">
            <a href="./index.html" class="sidebar-menu-item active">
                <span class="sidebar-menu-icon">🏠</span>
                <span>首页</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🔥</span>
                <span>热门</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">✨</span>
                <span>动态</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">📚</span>
                <span>订阅</span>
            </a>
        </div>
        
        <div class="sidebar-section-title">探索</div>
        <div class="sidebar-menu">
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">📺</span>
                <span>番剧</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🎬</span>
                <span>电影</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🎮</span>
                <span>游戏</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🎵</span>
                <span>音乐</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🎭</span>
                <span>鬼畜</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">💄</span>
                <span>时尚</span>
            </a>
        </div>
        
        <div class="sidebar-section-title">更多分类</div>
        <div class="sidebar-menu">
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">📱</span>
                <span>科技</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🍕</span>
                <span>美食</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">✈️</span>
                <span>旅游</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">💡</span>
                <span>知识</span>
            </a>
        </div>
        
        <div class="sidebar-section-title">频道</div>
        <div class="sidebar-menu">
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">📺</span>
                <span>直播</span>
            </a>
            <a href="" class="sidebar-menu-item">
                <span class="sidebar-menu-icon">🌟</span>
                <span>精选</span>
            </a>
        </div>
    </aside>
    
    <!-- 侧边栏遮罩层 -->
    <div class="sidebar-overlay" id="sidebar-overlay"></div>

    <!-- 页面主体内容区域 -->
    <main>
        <!-- 视频网格容器 - 显示视频卡片和轮播图 -->
        <div class="video-grid-container">
            <!-- 轮播图容器 - 通过JavaScript从carousel.json加载数据 -->
            <div class="carousel-container">
                <div class="carousel">
                    <!-- 轮播图幻灯片将通过JavaScript动态生成 -->
                    <div class="carousel-arrow carousel-arrow-prev">❮</div>
                    <div class="carousel-arrow carousel-arrow-next">❯</div>
                </div>
                <div class="carousel-nav">
                    <!-- 轮播图导航点将通过JavaScript动态生成 -->
                </div>
            </div>
            <!-- 视频卡片将通过script.js从videos.json动态加载 -->
        </div>
        
        <!-- 页脚上方信息区域 - 免责声明等 -->
        <div class="footer-head">
            <p>
                本页面仅供学习使用，所有视频均来自网络，版权归原作者所有。
                <br>
                "Bilibili Hub"
            </p>
            <div class="disclaimer">
                <p><strong>免责声明：</strong>本项目仅作为前端技术学习与展示，与Pornhub和哔哩哔哩(Bilibili)官方没有任何关联。
                项目中使用的任何设计元素仅用于教育目的，不代表对相关品牌的任何认可或附属关系。</p>
            </div>
        </div>
    </main>

    <!-- 页面底部 - 链接和版权信息 -->
    <footer>
        <!-- 页脚内容区域 - 分为四列显示不同类别的链接 -->
        <div class="footer-content">
            <div class="footer-column">
                <h4>bilibili</h4>
                <ul>
                    <li><a href="">关于我们</a></li>
                    <li><a href="">加入我们</a></li>
                    <li><a href="">bilibili认证</a></li>
                    <li><a href="">隐私政策</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h4>bilibili创作</h4>
                <ul>
                    <li><a href="">专栏投稿</a></li>
                    <li><a href="">音频投稿</a></li>
                    <li><a href="">视频投稿</a></li>
                    <li><a href="">创作激励</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h4>帮助中心</h4>
                <ul>
                    <li><a href="">常见问题</a></li>
                    <li><a href="">侵权申诉</a></li>
                    <li><a href="">会员服务</a></li>
                    <li><a href="">客服中心</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h4>更多</h4>
                <ul class="social-links">
                    <li><a href="">活动中心</a></li>
                    <li><a href="">课堂</a></li>
                    <li><a href="">专题中心</a></li>
                    <li><a href="">用户反馈</a></li>
                </ul>
            </div>
        </div>
        
        <!-- 版权信息区域 -->
        <div class="footer-bottom">
            <p>&copy; 2025 Bili-Hub "版权所有"</p>
        </div>
    </footer>
    
    <!-- JavaScript文件引用 -->
    <script src="./script.js"></script>

</body>

</html>

<!--
施工规划：
    当指针悬停于视频封面时，预览视频的功能
    自动获取b站用户头像文件(或者说能否自动获取对应图像url?)
    多页面切换功能
-->
